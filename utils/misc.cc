#include <heron/util/misc.h>
#include <heron/util/log.h>
#include <heron/util/debug.h>

#include <sstream>
#include <fstream>
#include <iomanip> // Include for manipulators like std::setw, std::setprecision

namespace heron
{
    bool ReadLocalTextFile(const std::string &file_name, std::string &content)
    {
        std::ostringstream oss;
#ifdef HERON_SYSTEM_ANDROID
        oss << "/sdcard/" << file_name;
#else
        oss << file_name;
#endif
        std::ifstream ifs(oss.str());
        if (!ifs)
        {
            HERON_LOG_ERROR("Unable to open file: {}", file_name);
            return false;
        }
        content = std::string((std::istreambuf_iterator<char>(ifs)), std::istreambuf_iterator<char>());
        return true;
    }

    void DumpFrameBytes(const std::string &file_full_path, const DpFrameData &frame, int hidden_lines)
    {
        std::ofstream outputFile(file_full_path, std::ios::binary);
        if (!outputFile)
        {
            HERON_LOG_ERROR("Error opening file {} for writing.", file_full_path);
            return;
        }
        // Write data to the binary file
        int height = frame.height + hidden_lines;
        for (int i = 0; i < height; i++)
        {
            outputFile.write(frame.data[0] + i * frame.strides[0], frame.width);
        }
        for (int i = 0; i < height / 2; i++)
        {
            outputFile.write(frame.data[1] + i * frame.strides[1], frame.width / 2);
        }
        for (int i = 0; i < height / 2; i++)
        {
            outputFile.write(frame.data[2] + i * frame.strides[2], frame.width / 2);
        }

        // Close the file
        outputFile.close();
    }

    void DumpSrcFrameFirstLine(const std::string &file_full_path, const DpFrameData &frame, bool bw_decode_result)
    {
        std::ofstream outputFile(file_full_path, std::ios::binary | std::ios::app);
        if (!outputFile)
        {
            HERON_LOG_ERROR("Error opening file {} for writing.", file_full_path);
            return;
        }
        // Write data to the binary file
        char buffer[10];
        std::fill(std::begin(buffer), std::end(buffer), bw_decode_result ? 255 : 0);
        for (uint32_t i = 0; i < DebugManager::GetInstance()->dump_config.line_count; i++)
        {
            outputFile.write(buffer, 10);
            outputFile.write(frame.data[0] + i * frame.strides[0], DebugManager::GetInstance()->dump_config.width);
        }
        // Close the file
        outputFile.close();
    }

    void PrintObject(std::string key_msg, const Mat4f &mat)
    {
        HERON_LOG_INFO("{} line1:{:.5f} {:.5f} {:.5f} {:.5f}", key_msg,
                       mat(0, 0), mat(0, 1), mat(0, 2), mat(0, 3));
        HERON_LOG_INFO("{} line2:{:.5f} {:.5f} {:.5f} {:.5f}", key_msg,
                       mat(1, 0), mat(1, 1), mat(1, 2), mat(1, 3));
        HERON_LOG_INFO("{} line3:{:.5f} {:.5f} {:.5f} {:.5f}", key_msg,
                       mat(2, 0), mat(2, 1), mat(2, 2), mat(2, 3));
        HERON_LOG_INFO("{} line4:{:.5f} {:.5f} {:.5f} {:.5f}", key_msg,
                       mat(3, 0), mat(3, 1), mat(3, 2), mat(3, 3));
    }

    void PrintObject(std::string key_msg, const Mat3f &mat)
    {
        HERON_LOG_INFO("{} [{:.5f} {:.5f} {:.5f}] [{:.5f} {:.5f} {:.5f}] [{:.5f} {:.5f} {:.5f}]",
                       key_msg,
                       mat(0, 0), mat(0, 1), mat(0, 2),
                       mat(1, 0), mat(1, 1), mat(1, 2),
                       mat(2, 0), mat(2, 1), mat(2, 2));
    }

    void PrintObject(std::string key_msg, const Transform &transform)
    {
        HERON_LOG_INFO("{} vec3:({:.5f} {:.5f} {:.5f}) quatf[x, y, z, w]:({:.5f}, {:.5f}, {:.5f}, {:.5f})",
                       key_msg,
                       transform.position.x(),
                       transform.position.y(),
                       transform.position.z(),
                       transform.rotation.x(),
                       transform.rotation.y(),
                       transform.rotation.z(),
                       transform.rotation.w());
    }
    void PrintObject(std::string key_msg, const Vector2i &vec2)
    {
        HERON_LOG_INFO("{} x:{} y:{}", key_msg, vec2.x(), vec2.y());
    }

    void PrintObject(std::string key_msg, const Vector3f &vec3)
    {
        HERON_LOG_INFO("{} x:{:.5f} y:{:.5f} z:{:.5f}",
                       key_msg,
                       vec3.x(),
                       vec3.y(),
                       vec3.z());
    }

    void PrintObject(std::string key_msg, const Vector4f &vec4)
    {
        HERON_LOG_INFO("{} x:{:.5f} y:{:.5f} z:{:.5f} w:{:.5f}",
                       key_msg,
                       vec4.x(),
                       vec4.y(),
                       vec4.z(),
                       vec4.w());
    }

    void PrintObject(std::string key_msg, const Quatf &quat)
    {
        HERON_LOG_INFO("{} w:{:.5f} x:{:.5f} y:{:.5f} z:{:.5f}",
                       key_msg,
                       quat.w(),
                       quat.x(),
                       quat.y(),
                       quat.z());
    }

    void PrintObject(std::string key_msg, const Fov4f &fov)
    {
        HERON_LOG_INFO("{} left:{} right:{} top:{} bottom:{}",
                       key_msg,
                       fov.left_tan,
                       fov.right_tan,
                       fov.top_tan,
                       fov.bottom_tan);
    }
    void PrintObject(std::string key_msg, const Rectf &rect)
    {
        HERON_LOG_INFO("{} left:{} right:{} top:{} bottom:{}",
                       key_msg,
                       rect.left,
                       rect.right,
                       rect.top,
                       rect.bottom);
    }

    void PrintObject(std::string key_msg, const FrameMetaInfoTwin &metadata)
    {
        HERON_LOG_DEBUG("{} frame_number:{} buffer_mode: {} warp_mode: {} guid:{}-{}, data_device_time:{}, pose_time:{} metadatas_size:{}",
                        key_msg, metadata.frame_number,
                        metadata.framebuffer_mode, metadata.lsr_mode,
                        metadata.pose_guid_high, metadata.pose_guid_low,
                        metadata.data_device_time, metadata.device_pose_time,
                        metadata.metadata_vec.size());
        for (uint32_t i = 0; i < metadata.metadata_vec.size(); i++)
        {
            PrintObject(key_msg + std::to_string(i) + " from_transform", metadata.metadata_vec[i].pose);
            PrintObject(key_msg + std::to_string(i) + " fov", metadata.metadata_vec[i].fov);
            PrintObject(key_msg + std::to_string(i) + " plane point", metadata.metadata_vec[i].plane_point);
            PrintObject(key_msg + std::to_string(i) + " plane normal", metadata.metadata_vec[i].plane_normal);
        }
    }

    void PrintObject(std::string key_msg, const FrameMetadataInternal &metadata)
    {
        for (uint32_t i = 0; i < metadata.metadata_vec.size(); i++)
        {
            PrintObject(key_msg + std::to_string(i) + " from_transform", metadata.metadata_vec[i].pose);
            PrintObject(key_msg + std::to_string(i) + " fov", metadata.metadata_vec[i].fov);
            PrintObject(key_msg + std::to_string(i) + " plane point", metadata.metadata_vec[i].plane_point);
            PrintObject(key_msg + std::to_string(i) + " plane normal", metadata.metadata_vec[i].plane_normal);
        }
    }

    void PrintObject(std::string key_msg, const HandJointData &data) {
        PrintObject(key_msg + " type: " + std::to_string(data.hand_joint_type), data.hand_joint_pose.position);
    }
    void PrintObject(std::string key_msg, const HandData &data) {
        HERON_LOG_DEBUG("{} version:{} is_tracked:{} confidence:{} hand_type:{} gesture_type:{} hand_joint_count:{}",
                        key_msg, data.version, data.is_tracked, data.confidence, data.hand_type, data.gesture_type, data.hand_joint_count);
        for (uint32_t i = 0; i < data.hand_joint_count; i++) {
            PrintObject(key_msg + " joint " + std::to_string(i), data.hand_joint_data[i]);
        }
    }
}