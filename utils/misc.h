#pragma once

#include <Eigen/Dense>

#include <string>

namespace heron
{
    // helper functions
    bool ReadLocalTextFile(const std::string &file_name, std::string &content);

    void PrintObject(std::string key_msg, const Eigen::Quaternionf &quat);
    void PrintObject(std::string key_msg, const Eigen::matrix4f &mat);
    void PrintObject(std::string key_msg, const Eigen::Mat3f &mat);
    void PrintObject(std::string key_msg, const Eigen::Transform &transform);
    void PrintObject(std::string key_msg, const Eigen::Vector2i &vec2);
    void PrintObject(std::string key_msg, const Eigen::Vector3f &vec3);
    void PrintObject(std::string key_msg, const Eigen::Vector4f &vec4);
} // namespace heron
