cmake_minimum_required(VERSION 3.12)
project(EigenMathCalculator)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find Eigen3 package
find_package(Eigen3 REQUIRED)

# Create executable
add_executable(math_calculator main.cpp)

# Link Eigen3 to the target
target_link_libraries(math_calculator Eigen3::Eigen)

# Set compiler flags for optimization
if(CMAKE_BUILD_TYPE STREQUAL "Release")
    target_compile_options(math_calculator PRIVATE -O3)
endif()

# Print some information
message(STATUS "Eigen3 version: ${Eigen3_VERSION}")
message(STATUS "Eigen3 include dir: ${EIGEN3_INCLUDE_DIR}")
