# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.30

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/Cellar/cmake/3.30.2/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/Cellar/cmake/3.30.2/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/WorkSpace/playground/eigen

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/WorkSpace/playground/eigen/build

# Include any dependencies generated for this target.
include CMakeFiles/math_calculator.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/math_calculator.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/math_calculator.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/math_calculator.dir/flags.make

CMakeFiles/math_calculator.dir/main.cpp.o: CMakeFiles/math_calculator.dir/flags.make
CMakeFiles/math_calculator.dir/main.cpp.o: /Users/<USER>/WorkSpace/playground/eigen/main.cpp
CMakeFiles/math_calculator.dir/main.cpp.o: CMakeFiles/math_calculator.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/WorkSpace/playground/eigen/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/math_calculator.dir/main.cpp.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/math_calculator.dir/main.cpp.o -MF CMakeFiles/math_calculator.dir/main.cpp.o.d -o CMakeFiles/math_calculator.dir/main.cpp.o -c /Users/<USER>/WorkSpace/playground/eigen/main.cpp

CMakeFiles/math_calculator.dir/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/math_calculator.dir/main.cpp.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/WorkSpace/playground/eigen/main.cpp > CMakeFiles/math_calculator.dir/main.cpp.i

CMakeFiles/math_calculator.dir/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/math_calculator.dir/main.cpp.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/WorkSpace/playground/eigen/main.cpp -o CMakeFiles/math_calculator.dir/main.cpp.s

# Object files for target math_calculator
math_calculator_OBJECTS = \
"CMakeFiles/math_calculator.dir/main.cpp.o"

# External object files for target math_calculator
math_calculator_EXTERNAL_OBJECTS =

math_calculator: CMakeFiles/math_calculator.dir/main.cpp.o
math_calculator: CMakeFiles/math_calculator.dir/build.make
math_calculator: CMakeFiles/math_calculator.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/WorkSpace/playground/eigen/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable math_calculator"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/math_calculator.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/math_calculator.dir/build: math_calculator
.PHONY : CMakeFiles/math_calculator.dir/build

CMakeFiles/math_calculator.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/math_calculator.dir/cmake_clean.cmake
.PHONY : CMakeFiles/math_calculator.dir/clean

CMakeFiles/math_calculator.dir/depend:
	cd /Users/<USER>/WorkSpace/playground/eigen/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/WorkSpace/playground/eigen /Users/<USER>/WorkSpace/playground/eigen /Users/<USER>/WorkSpace/playground/eigen/build /Users/<USER>/WorkSpace/playground/eigen/build /Users/<USER>/WorkSpace/playground/eigen/build/CMakeFiles/math_calculator.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/math_calculator.dir/depend

