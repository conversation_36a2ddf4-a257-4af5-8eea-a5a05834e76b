# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.30

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/Cellar/cmake/3.30.2/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/Cellar/cmake/3.30.2/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/WorkSpace/playground/eigen

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/WorkSpace/playground/eigen/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/math_calculator.dir/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/math_calculator.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/math_calculator.dir

# All Build rule for target.
CMakeFiles/math_calculator.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/math_calculator.dir/build.make CMakeFiles/math_calculator.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/math_calculator.dir/build.make CMakeFiles/math_calculator.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/WorkSpace/playground/eigen/build/CMakeFiles --progress-num=1,2 "Built target math_calculator"
.PHONY : CMakeFiles/math_calculator.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/math_calculator.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/WorkSpace/playground/eigen/build/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/math_calculator.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/WorkSpace/playground/eigen/build/CMakeFiles 0
.PHONY : CMakeFiles/math_calculator.dir/rule

# Convenience name for target.
math_calculator: CMakeFiles/math_calculator.dir/rule
.PHONY : math_calculator

# clean rule for target.
CMakeFiles/math_calculator.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/math_calculator.dir/build.make CMakeFiles/math_calculator.dir/clean
.PHONY : CMakeFiles/math_calculator.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

