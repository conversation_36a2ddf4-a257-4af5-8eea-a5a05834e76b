# Eigen Math Calculator

A CMake project demonstrating mathematical calculations using the Eigen library.

## Prerequisites

- CMake (version 3.12 or higher)
- C++ compiler with C++17 support
- Eigen3 library

### Installing Eigen3

**On macOS (using Homebrew):**
```bash
brew install eigen
```

**On Ubuntu/Debian:**
```bash
sudo apt-get install libeigen3-dev
```

**On CentOS/RHEL:**
```bash
sudo yum install eigen3-devel
```

## Building and Running

### Option 1: Using the build script
```bash
chmod +x build.sh
./build.sh
```

### Option 2: Manual build
```bash
mkdir build
cd build
cmake ..
make
./math_calculator
```

## Features

The program demonstrates various mathematical operations using Eigen:

1. **Basic Matrix Operations**
   - Matrix addition and multiplication
   - Matrix transpose
   - Determinant calculation

2. **Vector Operations**
   - Dot product and cross product
   - Vector norms and normalization

3. **Eigenvalue Decomposition**
   - Computing eigenvalues and eigenvectors

4. **Linear System Solving**
   - Solving Ax = b using LU decomposition
   - Solution verification

5. **Matrix Decompositions**
   - Singular Value Decomposition (SVD)
   - QR decomposition

6. **Statistical Operations**
   - Column-wise and row-wise statistics
   - Mean and sum calculations

## Project Structure

```
.
├── CMakeLists.txt    # CMake configuration
├── main.cpp          # Main source file with math calculations
├── build.sh          # Build script
└── README.md         # This file
```

## Customization

You can modify `main.cpp` to add your own mathematical calculations. The project is set up to easily incorporate additional Eigen functionality.
